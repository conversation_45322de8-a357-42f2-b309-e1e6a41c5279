import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def calculate_technical_indicators(df):
    """
    计算技术指标（不使用talib，使用pandas实现）
    """
    # 移动平均线
    df['ma_10'] = df['收盘'].rolling(window=10).mean()
    df['ma_20'] = df['收盘'].rolling(window=20).mean()
    df['ma_30'] = df['收盘'].rolling(window=30).mean()

    # 指数移动平均线
    df['ema_20'] = df['收盘'].ewm(span=20).mean()

    # 布林带
    rolling_mean = df['收盘'].rolling(window=20).mean()
    rolling_std = df['收盘'].rolling(window=20).std()
    df['boll_ub'] = rolling_mean + (rolling_std * 2)
    df['boll_lb'] = rolling_mean - (rolling_std * 2)
    df['boll_mid'] = rolling_mean

    # 乖离率 BIAS
    df['BIAS1'] = (df['收盘'] - df['ma_10']) / df['ma_10'] * 100
    df['BIAS2'] = (df['收盘'] - df['ma_20']) / df['ma_20'] * 100
    df['BIAS3'] = (df['收盘'] - df['ma_30']) / df['ma_30'] * 100

    # 动量指标 MTM (价格动量)
    df['MTM'] = df['收盘'] - df['收盘'].shift(10)

    # 收益率
    df['收益'] = df['收盘'].pct_change()

    return df

def preprocess_maotai_data(file_path):
    """
    预处理贵州茅台数据
    """
    # 读取Excel文件
    df = pd.read_excel(file_path)
    
    # 检查列名并重命名（如果需要）
    print("原始列名:", df.columns.tolist())
    
    # 假设Excel文件包含日期、开盘、最高、最低、收盘、成交量、成交额等列
    # 如果列名不同，请根据实际情况调整
    expected_columns = ['日期', '开盘', '最高', '最低', '收盘', '成交量', '成交额']
    
    # 检查是否包含必要的列
    for col in ['开盘', '最高', '最低', '收盘', '成交量']:
        if col not in df.columns:
            print(f"警告: 缺少列 '{col}'")
    
    # 确保日期列存在并转换为datetime
    if '日期' in df.columns:
        df['date'] = pd.to_datetime(df['日期'])
    elif 'Date' in df.columns:
        df['date'] = pd.to_datetime(df['Date'])
    else:
        # 如果没有日期列，创建一个
        df['date'] = pd.date_range(start='2020-01-01', periods=len(df), freq='D')
    
    # 按日期排序
    df = df.sort_values('date').reset_index(drop=True)
    
    # 计算技术指标
    df = calculate_technical_indicators(df)
    
    # 删除包含NaN的行
    df = df.dropna().reset_index(drop=True)
    
    # 选择特征列
    feature_columns = ['开盘', '最高', '最低', '收盘', '成交量', '成交额', '收益', 
                      'ma_10', 'ma_20', 'ema_20', 'boll_ub', 'boll_lb', 
                      'BIAS1', 'BIAS2', 'BIAS3', 'MTM']
    
    # 检查特征列是否存在
    available_features = [col for col in feature_columns if col in df.columns]
    print(f"可用特征: {available_features}")
    
    # 创建最终的数据框
    final_df = df[['date'] + available_features].copy()
    
    return final_df

def create_csv_for_fedformer(df, output_path='maotai_processed.csv'):
    """
    创建适合FEDformer的CSV文件
    """
    # 重新排列列，确保date在第一列，目标变量在最后一列
    columns = df.columns.tolist()
    columns.remove('date')
    if '收盘' in columns:
        columns.remove('收盘')
        columns = ['date'] + columns + ['收盘']
    else:
        columns = ['date'] + columns
    
    df_final = df[columns].copy()
    
    # 保存为CSV
    df_final.to_csv(output_path, index=False)
    print(f"处理后的数据已保存到: {output_path}")
    print(f"数据形状: {df_final.shape}")
    print(f"列名: {df_final.columns.tolist()}")
    
    return df_final

if __name__ == "__main__":
    # 处理贵州茅台数据
    try:
        df = preprocess_maotai_data('贵州茅台.xlsx')
        final_df = create_csv_for_fedformer(df)
        
        # 显示数据统计信息
        print("\n数据统计信息:")
        print(final_df.describe())
        
        # 显示前几行数据
        print("\n前5行数据:")
        print(final_df.head())
        
    except Exception as e:
        print(f"处理数据时出错: {e}")
        print("请检查Excel文件是否存在且格式正确")
