import argparse
import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from exp.exp_main import Exp_Main
import random
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def calculate_metrics(y_true, y_pred):
    """
    计算评估指标
    """
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2
    }

def plot_predictions(y_true, y_pred, title="True vs Predicted Closing Prices"):
    """
    绘制预测结果对比图
    """
    plt.figure(figsize=(15, 8))
    
    # 绘制真实值和预测值
    plt.plot(range(len(y_true)), y_true, label='True Closing Price', color='blue', alpha=0.7)
    plt.plot(range(len(y_pred)), y_pred, label='Predicted Closing Price', color='orange', alpha=0.7)
    
    plt.title(title, fontsize=16)
    plt.xlabel('Days', fontsize=12)
    plt.ylabel('Price', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 添加评估指标到图中
    metrics = calculate_metrics(y_true, y_pred)
    textstr = f"MSE: {metrics['MSE']:.4f}\nRMSE: {metrics['RMSE']:.4f}\nMAE: {metrics['MAE']:.4f}\nR²: {metrics['R2']:.4f}"
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
    plt.text(0.02, 0.98, textstr, transform=plt.gca().transAxes, fontsize=10,
             verticalalignment='top', bbox=props)
    
    plt.tight_layout()
    plt.savefig('maotai_prediction_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return metrics

class MaotaiExperiment:
    def __init__(self):
        self.args = self.get_args()
        self.exp = None
        
    def get_args(self):
        """
        设置实验参数
        """
        parser = argparse.ArgumentParser(description='FEDformer for Maotai Stock Prediction')

        # 基本配置
        parser.add_argument('--is_training', type=int, default=1, help='status')
        parser.add_argument('--task_id', type=str, default='maotai_stock', help='task id')
        parser.add_argument('--model', type=str, default='FEDformer',
                           help='model name, options: [FEDformer, Autoformer, Informer, Transformer]')
        
        # FEDformer特定配置
        parser.add_argument('--version', type=str, default='Fourier', help='FEDformer version')
        parser.add_argument('--mode_select', type=str, default='random', help='mode selection method')
        parser.add_argument('--modes', type=int, default=32, help='modes to be selected')
        parser.add_argument('--L', type=int, default=3, help='ignore level')
        parser.add_argument('--base', type=str, default='legendre', help='mwt base')
        parser.add_argument('--cross_activation', type=str, default='tanh', help='activation function')
        
        # 数据配置
        parser.add_argument('--data', type=str, default='maotai', help='dataset type')
        parser.add_argument('--root_path', type=str, default='./', help='root path of data file')
        parser.add_argument('--data_path', type=str, default='maotai_processed.csv', help='data file')
        parser.add_argument('--features', type=str, default='M', help='forecasting task')
        parser.add_argument('--target', type=str, default='收盘', help='target feature')
        parser.add_argument('--freq', type=str, default='d', help='freq for time features encoding')
        parser.add_argument('--checkpoints', type=str, default='./checkpoints/', help='location of model checkpoints')
        
        # 预测任务配置
        parser.add_argument('--seq_len', type=int, default=60, help='input sequence length')
        parser.add_argument('--label_len', type=int, default=30, help='start token length')
        parser.add_argument('--pred_len', type=int, default=1, help='prediction sequence length')
        
        # 模型配置
        parser.add_argument('--enc_in', type=int, default=16, help='encoder input size')
        parser.add_argument('--dec_in', type=int, default=16, help='decoder input size')
        parser.add_argument('--c_out', type=int, default=1, help='output size')
        parser.add_argument('--d_model', type=int, default=256, help='dimension of model')
        parser.add_argument('--n_heads', type=int, default=8, help='num of heads')
        parser.add_argument('--e_layers', type=int, default=2, help='num of encoder layers')
        parser.add_argument('--d_layers', type=int, default=1, help='num of decoder layers')
        parser.add_argument('--d_ff', type=int, default=1024, help='dimension of fcn')
        parser.add_argument('--moving_avg', default=[24], help='window size of moving average')
        parser.add_argument('--factor', type=int, default=1, help='attn factor')
        parser.add_argument('--distil', action='store_false', default=True, help='whether to use distilling')
        parser.add_argument('--dropout', type=float, default=0.1, help='dropout')
        parser.add_argument('--embed', type=str, default='timeF', help='time features encoding')
        parser.add_argument('--activation', type=str, default='gelu', help='activation')
        parser.add_argument('--output_attention', action='store_true', help='whether to output attention')
        
        # 优化配置
        parser.add_argument('--num_workers', type=int, default=0, help='data loader num workers')
        parser.add_argument('--itr', type=int, default=1, help='experiments times')
        parser.add_argument('--train_epochs', type=int, default=20, help='train epochs')
        parser.add_argument('--batch_size', type=int, default=16, help='batch size')
        parser.add_argument('--patience', type=int, default=5, help='early stopping patience')
        parser.add_argument('--learning_rate', type=float, default=0.0001, help='optimizer learning rate')
        parser.add_argument('--des', type=str, default='maotai_exp', help='exp description')
        parser.add_argument('--loss', type=str, default='mse', help='loss function')
        parser.add_argument('--lradj', type=str, default='type1', help='adjust learning rate')
        parser.add_argument('--use_amp', action='store_true', help='use automatic mixed precision', default=False)
        
        # GPU配置
        parser.add_argument('--use_gpu', type=bool, default=True, help='use gpu')
        parser.add_argument('--gpu', type=int, default=0, help='gpu')
        parser.add_argument('--use_multi_gpu', action='store_true', help='use multiple gpus', default=False)
        parser.add_argument('--devices', type=str, default='0', help='device ids of multi gpus')
        
        args = parser.parse_args()  # 解析命令行参数

        # 根据模型名称设置task_id
        args.task_id = f'maotai_{args.model.lower()}'
        
        # GPU设置
        args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False
        
        return args
    
    def run_experiment(self):
        """
        运行完整的实验
        """
        # 设置随机种子
        fix_seed = 2021
        random.seed(fix_seed)
        torch.manual_seed(fix_seed)
        np.random.seed(fix_seed)
        
        print("开始茅台股票预测实验...")
        print(f"使用模型: {self.args.model}")
        print(f"序列长度: {self.args.seq_len}")
        print(f"预测长度: {self.args.pred_len}")
        print(f"特征数量: {self.args.enc_in}")
        
        # 创建实验实例
        self.exp = Exp_Main(self.args)
        
        # 生成设置字符串
        setting = f'{self.args.task_id}_{self.args.model}_{self.args.data}_ft{self.args.features}_sl{self.args.seq_len}_ll{self.args.label_len}_pl{self.args.pred_len}_dm{self.args.d_model}_nh{self.args.n_heads}_el{self.args.e_layers}_dl{self.args.d_layers}_df{self.args.d_ff}_fc{self.args.factor}_eb{self.args.embed}_dt{self.args.distil}_{self.args.des}_0'
        
        print(f"实验设置: {setting}")
        
        # 训练模型
        print("开始训练...")
        self.exp.train(setting)
        
        # 测试模型并获取预测结果
        print("开始测试...")
        preds, trues = self.test_and_get_predictions(setting)
        
        # 反归一化
        print("反归一化预测结果...")
        preds_denorm, trues_denorm = self.denormalize_predictions(preds, trues)
        
        # 计算评估指标
        print("计算评估指标...")
        metrics = calculate_metrics(trues_denorm, preds_denorm)
        
        # 打印结果
        print("\n=== 评估结果 ===")
        for metric, value in metrics.items():
            print(f"{metric}: {value:.4f}")
        
        # 绘制结果
        print("绘制预测结果...")
        plot_predictions(trues_denorm, preds_denorm)
        
        return metrics, preds_denorm, trues_denorm

    def test_and_get_predictions(self, setting):
        """
        测试模型并获取预测结果
        """
        test_data, test_loader = self.exp._get_data(flag='test')

        # 加载最佳模型
        path = os.path.join(self.args.checkpoints, setting)
        best_model_path = path + '/' + 'checkpoint.pth'
        self.exp.model.load_state_dict(torch.load(best_model_path))

        preds = []
        trues = []

        self.exp.model.eval()
        with torch.no_grad():
            for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(test_loader):
                batch_x = batch_x.float().to(self.exp.device)
                batch_y = batch_y.float().to(self.exp.device)
                batch_x_mark = batch_x_mark.float().to(self.exp.device)
                batch_y_mark = batch_y_mark.float().to(self.exp.device)

                # decoder input
                dec_inp = torch.zeros_like(batch_y[:, -self.args.pred_len:, :]).float()
                dec_inp = torch.cat([batch_y[:, :self.args.label_len, :], dec_inp], dim=1).float().to(self.exp.device)

                # encoder - decoder
                if self.args.output_attention:
                    outputs = self.exp.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]
                else:
                    outputs = self.exp.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)

                # 只取目标变量（收盘价）
                if self.args.features == 'MS':
                    batch_y = batch_y[:, -self.args.pred_len:, -1:].to(self.exp.device)
                elif self.args.features == 'M':
                    # 多变量预测，取最后一列（目标变量）
                    batch_y = batch_y[:, -self.args.pred_len:, -1:].to(self.exp.device)
                else:
                    # 单变量预测
                    batch_y = batch_y[:, -self.args.pred_len:, :].to(self.exp.device)

                outputs = outputs.detach().cpu().numpy()
                batch_y = batch_y.detach().cpu().numpy()

                preds.append(outputs)
                trues.append(batch_y)

        preds = np.concatenate(preds, axis=0)
        trues = np.concatenate(trues, axis=0)

        # 重塑为二维数组
        preds = preds.reshape(-1, preds.shape[-1])
        trues = trues.reshape(-1, trues.shape[-1])

        return preds, trues

    def denormalize_predictions(self, preds, trues):
        """
        反归一化预测结果
        """
        print(f"原始预测形状: {preds.shape}")
        print(f"原始真实值形状: {trues.shape}")

        # 获取测试数据集以访问缩放器
        test_data, _ = self.exp._get_data(flag='test')

        # 检查预测结果的维度
        if len(preds.shape) == 1:
            preds = preds.reshape(-1, 1)
        if len(trues.shape) == 1:
            trues = trues.reshape(-1, 1)

        print(f"重塑后预测形状: {preds.shape}")
        print(f"重塑后真实值形状: {trues.shape}")

        # 确保真实值也只有一列（目标变量）
        if trues.shape[1] > 1:
            print(f"真实值有{trues.shape[1]}列，只取最后一列（目标变量）")
            trues = trues[:, -1:]

        print(f"处理后预测形状: {preds.shape}")
        print(f"处理后真实值形状: {trues.shape}")

        # 对于单变量预测或多变量预测的目标列
        if self.args.features == 'S' or self.args.c_out == 1:
            # 单变量预测，直接使用缩放器
            if hasattr(test_data, 'target_scaler') and test_data.target_scaler is not None:
                print("使用目标变量缩放器进行反归一化")
                preds_denorm = test_data.target_scaler.inverse_transform(preds)
                trues_denorm = test_data.target_scaler.inverse_transform(trues)
            else:
                print("使用通用缩放器进行反归一化")
                # 创建与原始特征数量相同的数组
                num_features = test_data.scaler.n_features_in_
                preds_full = np.zeros((preds.shape[0], num_features))
                trues_full = np.zeros((trues.shape[0], num_features))

                # 将预测值放在目标变量的位置（假设是最后一列）
                preds_full[:, -1] = preds.flatten()
                trues_full[:, -1] = trues.flatten()

                # 反归一化
                preds_denorm_full = test_data.scaler.inverse_transform(preds_full)
                trues_denorm_full = test_data.scaler.inverse_transform(trues_full)

                # 只取目标变量列
                preds_denorm = preds_denorm_full[:, -1:]
                trues_denorm = trues_denorm_full[:, -1:]
        else:
            # 多变量预测
            print("多变量预测反归一化")
            if hasattr(test_data, 'target_scaler') and test_data.target_scaler is not None:
                print("使用目标变量缩放器")
                preds_denorm = test_data.target_scaler.inverse_transform(preds)
                trues_denorm = test_data.target_scaler.inverse_transform(trues)
            else:
                print("使用通用缩放器，需要扩展到完整特征")
                # 创建与原始特征数量相同的数组
                num_features = test_data.scaler.n_features_in_
                preds_full = np.zeros((preds.shape[0], num_features))
                trues_full = np.zeros((trues.shape[0], num_features))

                # 将预测值和真实值放在目标变量的位置（最后一列）
                preds_full[:, -1] = preds.flatten()
                trues_full[:, -1] = trues.flatten()

                # 反归一化
                preds_denorm_full = test_data.scaler.inverse_transform(preds_full)
                trues_denorm_full = test_data.scaler.inverse_transform(trues_full)

                # 只取目标变量列
                preds_denorm = preds_denorm_full[:, -1:]
                trues_denorm = trues_denorm_full[:, -1:]

        print(f"反归一化后预测形状: {preds_denorm.shape}")
        print(f"反归一化后真实值形状: {trues_denorm.shape}")

        return preds_denorm.flatten(), trues_denorm.flatten()


def main():
    """
    主函数
    """
    print("=== 贵州茅台股票价格预测实验 ===")

    # 检查数据文件是否存在
    if not os.path.exists('maotai_processed.csv'):
        print("未找到处理后的数据文件，请先运行 data_preprocessing.py")
        print("正在运行数据预处理...")

        # 导入并运行数据预处理
        try:
            from data_preprocessing import preprocess_maotai_data, create_csv_for_fedformer
            df = preprocess_maotai_data('贵州茅台.xlsx')
            create_csv_for_fedformer(df)
        except Exception as e:
            print(f"数据预处理失败: {e}")
            return

    # 创建并运行实验
    experiment = MaotaiExperiment()

    try:
        metrics, preds, trues = experiment.run_experiment()

        # 保存结果
        results_df = pd.DataFrame({
            'True_Price': trues,
            'Predicted_Price': preds
        })
        results_df.to_csv('maotai_prediction_results.csv', index=False)
        print("预测结果已保存到 maotai_prediction_results.csv")

        # 保存评估指标
        with open('maotai_metrics.txt', 'w', encoding='utf-8') as f:
            f.write("=== 贵州茅台股票预测评估指标 ===\n")
            for metric, value in metrics.items():
                f.write(f"{metric}: {value:.4f}\n")

        print("实验完成！")

    except Exception as e:
        print(f"实验过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
