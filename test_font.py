import matplotlib.pyplot as plt
import matplotlib
import numpy as np

# 检查可用字体
print("可用字体:")
font_list = matplotlib.font_manager.get_font_names()
chinese_fonts = [font for font in font_list if any(keyword in font.lower() for keyword in ['simhei', 'microsoft', 'yahei', 'song', 'kai'])]
print("中文字体:", chinese_fonts[:10])

# 设置字体
matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 测试绘图
plt.figure(figsize=(10, 6))
x = np.linspace(0, 10, 100)
y1 = np.sin(x)
y2 = np.cos(x)

plt.plot(x, y1, label='真实价格', linewidth=2)
plt.plot(x, y2, label='预测价格', linewidth=2)
plt.title('茅台股票价格预测测试')
plt.xlabel('时间')
plt.ylabel('价格')
plt.legend()
plt.grid(True, alpha=0.3)

# 添加文本框
textstr = "MSE: 1234.56\nRMSE: 35.13\nMAE: 28.45\nR²: 0.8765"
props = dict(boxstyle='round', facecolor='lightblue', alpha=0.8)
plt.text(0.02, 0.98, textstr, transform=plt.gca().transAxes, fontsize=12,
         verticalalignment='top', bbox=props)

plt.tight_layout()
plt.savefig('font_test.png', dpi=300, bbox_inches='tight')
plt.show()

print("字体测试完成，请查看 font_test.png 文件")
