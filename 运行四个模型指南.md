# 贵州茅台股票预测 - 四个模型运行指南

## 环境准备
```bash
conda activate fedformer
```

## 数据预处理
首先运行数据预处理（只需运行一次）：
```bash
python data_preprocessing.py
```

## 如何指定运行哪个模型

**关键参数：`--model`**

这个参数决定使用哪个模型，有四个选项：
- `FEDformer` - FEDformer模型
- `Autoformer` - Autoformer模型
- `Informer` - Informer模型
- `Transformer` - Transformer模型

## 四个模型运行命令

**基础命令模板：**
```bash
python run.py --model [模型名称] --task_id [任务名称] [其他参数...]
```

### 1. 运行 FEDformer 模型
```bash
python run.py --is_training 1 --root_path ./ --data_path maotai_processed.csv --task_id maotai_fedformer --model FEDformer --data maotai --features M --seq_len 60 --label_len 30 --pred_len 1 --e_layers 2 --d_layers 1 --factor 3 --enc_in 16 --dec_in 16 --c_out 1 --des Exp --itr 1 --target 收盘 --train_epochs 20 --batch_size 16 --learning_rate 0.0001
```

### 2. 运行 Autoformer 模型
```bash
python run.py --is_training 1 --root_path ./ --data_path maotai_processed.csv --task_id maotai_autoformer --model Autoformer --data maotai --features M --seq_len 60 --label_len 30 --pred_len 1 --e_layers 2 --d_layers 1 --factor 3 --enc_in 16 --dec_in 16 --c_out 1 --des Exp --itr 1 --target 收盘 --train_epochs 20 --batch_size 16 --learning_rate 0.0001
```

### 3. 运行 Informer 模型
```bash
python run.py --is_training 1 --root_path ./ --data_path maotai_processed.csv --task_id maotai_informer --model Informer --data maotai --features M --seq_len 60 --label_len 30 --pred_len 1 --e_layers 2 --d_layers 1 --factor 3 --enc_in 16 --dec_in 16 --c_out 1 --des Exp --itr 1 --target 收盘 --train_epochs 20 --batch_size 16 --learning_rate 0.0001
```

### 4. 运行 Transformer 模型
```bash
python run.py --is_training 1 --root_path ./ --data_path maotai_processed.csv --task_id maotai_transformer --model Transformer --data maotai --features M --seq_len 60 --label_len 30 --pred_len 1 --e_layers 2 --d_layers 1 --factor 3 --enc_in 16 --dec_in 16 --c_out 1 --des Exp --itr 1 --target 收盘 --train_epochs 20 --batch_size 16 --learning_rate 0.0001
```

## 重要区别说明

每个命令中只有两个参数不同：

| 模型 | `--model` 参数 | `--task_id` 参数 |
|------|----------------|------------------|
| FEDformer | `FEDformer` | `maotai_fedformer` |
| Autoformer | `Autoformer` | `maotai_autoformer` |
| Informer | `Informer` | `maotai_informer` |
| Transformer | `Transformer` | `maotai_transformer` |

**说明：**
- `--model` 决定使用哪个模型算法
- `--task_id` 是任务标识符，用于区分不同实验的结果文件夹

## 参数说明

| 参数 | 说明 | 值 |
|------|------|-----|
| `--model` | 模型类型 | FEDformer/Autoformer/Informer/Transformer |
| `--data` | 数据集类型 | maotai |
| `--features` | 特征类型 | M (多变量预测) |
| `--seq_len` | 输入序列长度 | 60 (60天历史数据) |
| `--pred_len` | 预测长度 | 1 (预测1天) |
| `--enc_in` | 编码器输入维度 | 16 (特征数量) |
| `--target` | 目标变量 | 收盘 |
| `--train_epochs` | 训练轮数 | 20 |
| `--batch_size` | 批次大小 | 16 |

## 特征说明
模型使用的16个特征：
1. 开盘
2. 最高
3. 最低
4. 收盘
5. 成交量
6. 成交额
7. 收益
8. ma_10 (10日移动平均)
9. ma_20 (20日移动平均)
10. ema_20 (20日指数移动平均)
11. boll_ub (布林带上轨)
12. boll_lb (布林带下轨)
13. BIAS1 (乖离率1)
14. BIAS2 (乖离率2)
15. BIAS3 (乖离率3)
16. MTM (动量指标)

## 结果查看

每个模型运行完成后，结果保存在：
- `./results/模型名称_设置/` 目录下
- `metrics.npy` - 评估指标 (MAE, MSE, RMSE, MAPE, MSPE)
- `pred.npy` - 预测结果
- `true.npy` - 真实值

## 🎯 最简化运行方式（只传模型参数）

**只需要改这几个关键参数：**

### 1. 运行 FEDformer
```bash
python run.py --model FEDformer --data maotai --target 收盘 --data_path maotai_processed.csv --enc_in 16 --c_out 1
```

### 2. 运行 Autoformer
```bash
python run.py --model Autoformer --data maotai --target 收盘 --data_path maotai_processed.csv --enc_in 16 --c_out 1
```

### 3. 运行 Informer
```bash
python run.py --model Informer --data maotai --target 收盘 --data_path maotai_processed.csv --enc_in 16 --c_out 1
```

### 4. 运行 Transformer
```bash
python run.py --model Transformer --data maotai --target 收盘 --data_path maotai_processed.csv --enc_in 16 --c_out 1
```

## 🔥 超级简化版（只传模型名）

如果你已经把数据文件名改为默认的，甚至可以更简单：

```bash
# 只传模型名！
python run.py --model FEDformer
python run.py --model Autoformer
python run.py --model Informer
python run.py --model Transformer
```

**前提条件：**
1. 把 `maotai_processed.csv` 重命名为 `ETTh1.csv`
2. 把目标列 `收盘` 重命名为 `OT`

## 📝 批处理文件方式

创建四个 `.bat` 文件：

**run_fedformer.bat:**
```bash
python run.py --model FEDformer --data maotai --target 收盘 --data_path maotai_processed.csv --enc_in 16 --c_out 1
```

**run_autoformer.bat:**
```bash
python run.py --model Autoformer --data maotai --target 收盘 --data_path maotai_processed.csv --enc_in 16 --c_out 1
```

**run_informer.bat:**
```bash
python run.py --model Informer --data maotai --target 收盘 --data_path maotai_processed.csv --enc_in 16 --c_out 1
```

**run_transformer.bat:**
```bash
python run.py --model Transformer --data maotai --target 收盘 --data_path maotai_processed.csv --enc_in 16 --c_out 1
```

然后直接双击运行或在命令行输入：
```bash
run_fedformer.bat
run_autoformer.bat
run_informer.bat
run_transformer.bat
```

## 运行步骤总结

1. **数据预处理（只需运行一次）：**
   ```bash
   python data_preprocessing.py
   ```

2. **选择运行哪个模型：**
   - 想用FEDformer → 运行FEDformer命令
   - 想用Autoformer → 运行Autoformer命令
   - 想用Informer → 运行Informer命令
   - 想用Transformer → 运行Transformer命令

3. **查看结果：**
   - 控制台会显示训练过程和最终的MSE、MAE指标
   - 结果文件保存在 `./results/任务名称/` 文件夹中

## 快速对比四个模型
如果想快速对比四个模型的性能，可以依次运行上述四个命令，然后查看各自的 `result.txt` 文件中的评估指标。

## 注意事项
1. 确保已经运行了数据预处理脚本
2. 每个模型训练大约需要几分钟到十几分钟
3. 如果显存不足，可以减小 `batch_size` 参数
4. 如果想要更好的效果，可以增加 `train_epochs` 参数
5. **每次只能运行一个模型**，不要同时运行多个命令
