import argparse
import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from exp.exp_main import Exp_Main
import random
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def calculate_metrics(y_true, y_pred):
    """计算评估指标"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2
    }

def get_args():
    """设置实验参数"""
    parser = argparse.ArgumentParser(description='Quick Maotai Stock Prediction')
    
    # 基本配置
    parser.add_argument('--model', type=str, default='FEDformer', 
                       help='model name, options: [FEDformer, Autoformer, Informer, Transformer]')
    
    args = parser.parse_args()
    
    # 设置所有参数
    args.is_training = 1
    args.task_id = f'maotai_{args.model.lower()}'
    
    # FEDformer特定配置
    args.version = 'Fourier'
    args.mode_select = 'random'
    args.modes = 32
    args.L = 3
    args.base = 'legendre'
    args.cross_activation = 'tanh'
    
    # 数据配置
    args.data = 'maotai'
    args.root_path = './'
    args.data_path = 'maotai_processed.csv'
    args.features = 'M'
    args.target = '收盘'
    args.freq = 'd'
    args.checkpoints = './checkpoints/'
    
    # 预测任务配置
    args.seq_len = 60
    args.label_len = 30
    args.pred_len = 1
    
    # 模型配置
    args.enc_in = 16
    args.dec_in = 16
    args.c_out = 1
    args.d_model = 256
    args.n_heads = 8
    args.e_layers = 2
    args.d_layers = 1
    args.d_ff = 1024
    args.moving_avg = [24]
    args.factor = 1
    args.distil = True
    args.dropout = 0.1
    args.embed = 'timeF'
    args.activation = 'gelu'
    args.output_attention = False
    
    # 优化配置 - 快速版本
    args.num_workers = 0
    args.itr = 1
    args.train_epochs = 5  # 只训练5轮
    args.batch_size = 32   # 增大批次大小
    args.patience = 2      # 减少耐心值
    args.learning_rate = 0.001  # 增大学习率
    args.des = 'quick_test'
    args.loss = 'mse'
    args.lradj = 'type1'
    args.use_amp = False
    
    # GPU配置
    args.use_gpu = True if torch.cuda.is_available() else False
    args.gpu = 0
    args.use_multi_gpu = False
    args.devices = '0'
    
    return args

def main():
    """主函数"""
    print("=== 茅台股票预测快速测试 ===")
    
    # 检查数据文件
    if not os.path.exists('maotai_processed.csv'):
        print("未找到数据文件，正在运行数据预处理...")
        try:
            from data_preprocessing import preprocess_maotai_data, create_csv_for_fedformer
            df = preprocess_maotai_data('贵州茅台.xlsx')
            create_csv_for_fedformer(df)
        except Exception as e:
            print(f"数据预处理失败: {e}")
            return
    
    # 设置随机种子
    fix_seed = 2021
    random.seed(fix_seed)
    torch.manual_seed(fix_seed)
    np.random.seed(fix_seed)
    
    # 获取参数
    args = get_args()
    
    print(f"使用模型: {args.model}")
    print(f"训练轮数: {args.train_epochs}")
    print(f"早停耐心: {args.patience}")
    print(f"使用设备: {'GPU' if args.use_gpu else 'CPU'}")
    
    # 创建实验实例
    exp = Exp_Main(args)
    
    # 生成设置字符串
    setting = f'{args.task_id}_{args.model}_{args.data}_quick_test'
    
    print(f"开始训练...")
    
    try:
        # 训练模型
        exp.train(setting)
        
        # 测试模型
        print("开始测试...")
        exp.test(setting)
        
        print("快速测试完成！")
        print(f"结果保存在: ./results/{setting}/")
        
        # 读取并显示结果
        result_path = f'./results/{setting}/result.txt'
        if os.path.exists(result_path):
            with open(result_path, 'r') as f:
                result_content = f.read()
                print("\n=== 测试结果 ===")
                print(result_content)
        
    except KeyboardInterrupt:
        print("\n训练被用户中断")
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
