import pandas as pd
import numpy as np

# 测试数据预处理
print("=== 测试数据预处理 ===")
try:
    from data_preprocessing import preprocess_maotai_data, create_csv_for_fedformer
    
    # 重新处理数据
    df = preprocess_maotai_data('贵州茅台.xlsx')
    final_df = create_csv_for_fedformer(df)
    
    print("数据预处理成功！")
    print(f"最终数据形状: {final_df.shape}")
    print(f"列名: {final_df.columns.tolist()}")
    
    # 检查目标变量位置
    if '收盘' in final_df.columns:
        target_idx = final_df.columns.get_loc('收盘')
        print(f"目标变量 '收盘' 在第 {target_idx} 列（从0开始计数）")
        print(f"是否为最后一列: {target_idx == len(final_df.columns) - 1}")
    
    # 检查数据类型
    print("\n数据类型:")
    print(final_df.dtypes)
    
    # 检查是否有缺失值
    print(f"\n缺失值总数: {final_df.isnull().sum().sum()}")
    
except Exception as e:
    print(f"数据预处理失败: {e}")
    import traceback
    traceback.print_exc()

# 测试数据加载器
print("\n=== 测试数据加载器 ===")
try:
    from data_provider.data_loader import Dataset_Maotai
    
    # 创建数据集实例
    dataset = Dataset_Maotai(
        root_path='./',
        data_path='maotai_processed.csv',
        flag='train',
        features='M',
        target='收盘'
    )
    
    print(f"数据集长度: {len(dataset)}")
    print(f"数据形状: {dataset.data_x.shape}")
    print(f"是否有目标缩放器: {hasattr(dataset, 'target_scaler') and dataset.target_scaler is not None}")
    
    # 测试获取一个样本
    sample = dataset[0]
    print(f"样本形状: seq_x={sample[0].shape}, seq_y={sample[1].shape}")
    
except Exception as e:
    print(f"数据加载器测试失败: {e}")
    import traceback
    traceback.print_exc()
